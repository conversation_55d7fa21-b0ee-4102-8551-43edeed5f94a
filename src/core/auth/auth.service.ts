import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { compare, hash } from 'bcrypt';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { AUTH_CONSTANTS } from './domain/auth.constants';
import { LoginDto } from './dto/login.dto';
import { JwtPayload } from './domain/auth.types';
import {
  ANALYTICS_SERVICE,
  IAnalyticsService,
} from '@core/analytics/analytics.interface';
import {
  UserOrigin,
  UserStatus,
  UserType,
} from '@app/business/user/users/domain/user.types';
import { TenantEntity } from '@app/business/user/tenants/infrastructure/persistence/relational/entities/tenant.entity';
import { TenantRegistrationDto } from '@core/auth/dto/tenant-registration.dto';
import { CompanyIdService } from '@core/auth/services/company-id.service';
import { CompanyIdAvailabilityResponseDto } from '@core/auth/dto/check-company-id.dto';
import {
  CompanyIdAlreadyExistsException,
  EmailAlreadyExistsException,
  InvalidCredentialsException,
  RegistrationFailedException,
  TokenInvalidException,
} from '@utils/errors/exceptions/auth.exceptions';
import { AppException } from '@utils/errors/app.exception';
import { CryptoUtils } from '@utils/crypto.utils';
import { UserNotFoundException } from '@utils/errors/exceptions/user.exceptions';
import { ContactEntity } from '../../business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { ErrorCode } from '../../utils/errors/error-codes';

@Injectable()
export class AuthService {
  private readonly cryptoUtils: CryptoUtils;
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(TenantEntity)
    private readonly tenantRepository: Repository<TenantEntity>,
    private readonly jwtService: JwtService,
    private readonly connection: DataSource,
    private readonly companyIdService: CompanyIdService,
    @Inject(ANALYTICS_SERVICE)
    private readonly analyticsService: IAnalyticsService,
  ) {
    this.cryptoUtils = new CryptoUtils(AUTH_CONSTANTS.ENCRYPTION_KEY);
  }

  async login(loginDto: LoginDto) {
    const user = await this.userRepository.findOne({
      where: { email: loginDto.email, userType: UserType.Admin },
      select: ['id', 'email', 'password', 'status', 'userType', 'tenantId'],
    });

    if (!user) {
      throw new InvalidCredentialsException(loginDto.email);
    }

    const isPasswordValid = await compare(loginDto.password, user.password);
    if (!isPasswordValid) {
      throw new InvalidCredentialsException(loginDto.email);
    }

    // Update login info
    await this.userRepository.update(user.id, {
      lastLoginAt: new Date(),
      loginCount: () => 'login_count + 1',
    });

    const accessToken = await this.generateToken(user);

    // Track user sign-in event
    await this.analyticsService.trackEvent(user.id, 'user_signed_in', {
      userType: user.userType,
      tenantId: user.tenantId,
      email: user.email,
      timestamp: new Date().toISOString(),
    });

    return {
      user,
      accessToken,
    };
  }

  private async generateToken(user: UserEntity): Promise<string> {
    const publicData = {
      sub: user.id,
    };

    const sensitiveData = {
      userType: user.userType,
      tenantId: user.tenantId,
    };

    const payload: JwtPayload = {
      ...publicData,
      ctx: sensitiveData,
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      secret: AUTH_CONSTANTS.JWT_SECRET,
      expiresIn: AUTH_CONSTANTS.SESSION_EXPIRATION,
    });

    return accessToken;
  }

  async checkCompanyIdAvailability(
    companyName: string,
  ): Promise<CompanyIdAvailabilityResponseDto> {
    return this.companyIdService.checkCompanyIdAvailability(companyName);
  }

  async registerWithTenant(
    registrationDto: TenantRegistrationDto,
  ): Promise<{ user: UserEntity; tenant: TenantEntity; accessToken: string }> {
    const existingUser = await this.userRepository.findOne({
      where: { email: registrationDto.email },
    });

    if (existingUser) {
      throw new EmailAlreadyExistsException(registrationDto.email);
    }

    const existingTenant = await this.tenantRepository.findOne({
      where: { companyUniqueId: registrationDto.companyUniqueId },
    });
    if (existingTenant) {
      throw new CompanyIdAlreadyExistsException(
        registrationDto.companyUniqueId,
      );
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const newTenant: Partial<TenantEntity> = {
        name: registrationDto.companyName,
        companyUniqueId: registrationDto.companyUniqueId,
        timezone: registrationDto.timezone,
        contactEmail: registrationDto.contactEmail || registrationDto.email,
        status: true,
        settings: {},
        preferences: {},
      };

      const tenantRepository = queryRunner.manager.getRepository(TenantEntity);
      const tenant = tenantRepository.create(newTenant);
      const savedTenant = await tenantRepository.save(tenant);

      const hashedPassword = await hash(registrationDto.password, 10);

      const newUser: Partial<UserEntity> = {
        email: registrationDto.email,
        password: hashedPassword,
        companyName: registrationDto.name,
        contactName: registrationDto.name,
        tenantId: savedTenant.id,
        userType: UserType.Admin,
        status: UserStatus.Active,
        origin: UserOrigin.Local,
        emailVerified: true,
        loginCount: 0,
        failedAttempts: 0,
        isDeleted: false,
      };

      const userRepository = queryRunner.manager.getRepository(UserEntity);
      const user = userRepository.create(newUser);
      const savedUser = await userRepository.save(user);

      //add  one static custom package template for new tenant
      const customPackageTemplate = {
        tenantId: savedTenant.id,
        name: 'Custom Package',
        description: 'Standard package for most shipments',
        capabilities: ['Box', 'Envelope'],
        dimensionsRequired: true,
        weightRequired: true,
        priceCalculationRules: {
          "basePrice": 10,
          "weightFactor": 0.5,
          "distanceFactor": 0.1
        },
        specialHandlingInstructions: 'Handle with care',
        requiresSignature: false,
        requiresInsurance: false,
       // vehicleTypeRestrictions: ['Van', 'Truck'],
        availableZones: ['014c8624-6a50-4df1-8ee9-8e50072b8d87', '0caada6a-0766-4baf-b22b-0329dbe01bbb'],
        metadata: {
          isPrimary: true,
        },
        status: 'Active',
        createdBy: savedUser.id,
        updatedBy: savedUser.id,
      };

      const packageTemplateRepository = queryRunner.manager.getRepository(
        'PackageTemplateEntity',
      );
      const packageTemplate = packageTemplateRepository.create(customPackageTemplate);
      await packageTemplateRepository.save(packageTemplate);



      await queryRunner.commitTransaction();

      const accessToken = await this.generateToken(savedUser);

      // Track user registration event
      await this.analyticsService.trackEvent(savedUser.id, 'user_signed_up', {
        userType: savedUser.userType,
        tenantId: savedTenant.id,
        email: savedUser.email,
        companyName: savedTenant.name,
        timestamp: new Date().toISOString(),
      });

      return {
        user: savedUser,
        tenant: savedTenant,
        accessToken,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (error instanceof AppException) {
        throw error;
      }

      throw new RegistrationFailedException(
        error.message || 'Unknown error occurred',
        { originalError: error.message },
      );
    } finally {
      await queryRunner.release();
    }
  }

  async verifyToken(token: string): Promise<string> {
    try {
      const decodedJWT: JwtPayload | null = this.jwtService.decode(token);
      if (!decodedJWT || !decodedJWT.sub) {
        throw new TokenInvalidException('access');
      }

      const userInfo = await this.userRepository.findOneBy({
        id: decodedJWT.sub,
      });
      if (!userInfo) return '';

      return await this.generateToken(userInfo);
    } catch {
      throw new TokenInvalidException('access');
    }
  }
  async getCurrentUser(userId: string): Promise<Partial<UserEntity>> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.categories', 'categories')
      .where('user.id = :userId', { userId })
      .select([
        'user.id',
        'user.tenantId',
        'user.companyName',
        'user.contactName',
        'user.accountNumber',
        'user.phoneCountryCode',
        'user.phoneNumber',
        'user.phoneExtension',
        'user.faxCountryCode',
        'user.faxNumber',
        'user.website',
        'user.email',
        'user.emailVerified',
        'user.status',
        'user.lastLoginAt',
        'user.loginCount',
        'user.failedAttempts',
        'user.lockedUntil',
        'user.origin',
        'user.userType',
        'user.notificationSettings',
        'user.metadata',
        'user.preferences',
        'user.addressLine1',
        'user.addressLine2',
        'user.city',
        'user.province',
        'user.postalCode',
        'user.country',
        'user.isDeleted',
        'user.deletedAt',
        'user.createdAt',
        'user.updatedAt',
        'categories',
      ])
      .getOne();

    if (!user) {
      throw new UserNotFoundException(userId);
    }

    return user;
  }

  async loginContact(loginDto: LoginDto) {
    const contact = await this.connection.getRepository(ContactEntity).findOne({
      where: { email: loginDto.email },
      select: [
        'id',
        'email',
        'password',
        'name',
        'phoneNumber',
        'phoneCountryCode',
        'permissions',
        'isActive',
        'userId',
        'emailVerified',
      ],
    });

    if (!contact) {
      throw new InvalidCredentialsException(loginDto.email);
    }

    const isPasswordValid = await compare(loginDto.password, contact.password);
    if (!isPasswordValid) {
      throw new InvalidCredentialsException(loginDto.email);
    }

    if (!contact.isActive) {
      throw new AppException(
        'This contact account is inactive',
        ErrorCode.USER_INACTIVE,
        HttpStatus.FORBIDDEN,
      );
    }

    // Update login info
    await this.connection.getRepository(ContactEntity).update(contact.id, {
      lastLoginAt: new Date(),
      loginCount: () => 'login_count + 1',
    });

    const accessToken = await this.generateContactToken(contact);

    // Track contact sign-in event
    await this.analyticsService.trackEvent(contact.id, 'contact_signed_in', {
      customerId: contact.userId,
      email: contact.email,
      name: contact.name,
      timestamp: new Date().toISOString(),
    });

    return {
      contact: {
        id: contact.id,
        email: contact.email,
        name: contact.name,
        phoneNumber: contact.phoneNumber,
        phoneCountryCode: contact.phoneCountryCode,
        permissions: contact.permissions,
        isActive: contact.isActive,
      },
      accessToken,
    };
  }

  private async generateContactToken(contact: ContactEntity): Promise<string> {
    const publicData = {
      sub: contact.id,
    };

    const sensitiveData = {
      userType: 'contact',
      customerId: contact.userId,
      tenantId: contact.userId,
    };

    const payload: JwtPayload = {
      ...publicData,
      ctx: sensitiveData,
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      secret: AUTH_CONSTANTS.JWT_SECRET,
      expiresIn: AUTH_CONSTANTS.SESSION_EXPIRATION,
    });

    return accessToken;
  }
}
