import { Injectable } from '@nestjs/common';
import { TimeClockSessionRepository } from './infrastructure/repositories/time-clock-session.repository';
import { TimeClockSessionDomain } from './domain/time-clock-session';
import { VehicleDomain } from '@app/business/vehicle/vehicles/domain/vehicle';
import { VehicleRepository } from '@app/business/vehicle/vehicles/infrastructure/repositories/vehicle.repository';
import {
  TimeClockSessionNotFoundException,
  TimeClockSessionOverlapException,
  TimeClockSessionDurationExceededException,
  TimeClockSessionOperationNotAllowedException,
  VehicleNotFoundException,
} from '@app/utils/errors/exceptions/vehicle-exceptions';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { ClockInDto } from '../../mobile/timeclock/dto/clock-in.dto';

@Injectable()
export class TimeClockSessionService {
  private readonly MAX_SESSION_DURATION_HOURS = 24;

  constructor(
    private readonly timeClockSessionRepository: TimeClockSessionRepository,
    private readonly vehicleRepository: VehicleRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async clockIn(
    driverId: string,
    clockInDto: ClockInDto,
  ): Promise<TimeClockSessionDomain> {
    const timeClockSessionDomain = this.mapper.map(
      clockInDto,
      ClockInDto,
      TimeClockSessionDomain,
    );
    const vehicle = await this.vehicleRepository.findOne({
      id: timeClockSessionDomain.vehicleId,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(timeClockSessionDomain.vehicleId);
    }
    const tenantId = vehicle.tenantId;

    timeClockSessionDomain.driverId = driverId;
    timeClockSessionDomain.tenantId = tenantId;
    timeClockSessionDomain.source = 'system';
    timeClockSessionDomain.startOdometer = clockInDto.odometer;
    timeClockSessionDomain.startTime = new Date();
    return await this.create(timeClockSessionDomain);
  }

  async create(
    timeClockSessionDomain: TimeClockSessionDomain,
  ): Promise<TimeClockSessionDomain> {
    const vehicle = await this.vehicleRepository.findOne({
      id: timeClockSessionDomain.vehicleId,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(timeClockSessionDomain.vehicleId);
    }

    if (timeClockSessionDomain.endTime) {
      const duration =
        (new Date(timeClockSessionDomain.endTime).getTime() -
          new Date(timeClockSessionDomain.startTime).getTime()) /
        (1000 * 60 * 60);

      if (duration > this.MAX_SESSION_DURATION_HOURS) {
        throw new TimeClockSessionDurationExceededException(
          timeClockSessionDomain.id,
          duration,
          this.MAX_SESSION_DURATION_HOURS,
        );
      }

      if (duration <= 0) {
        throw new TimeClockSessionOperationNotAllowedException(
          '',
          'create',
          'End time must be after start time',
        );
      }
    }

    const overlappingSession =
      await this.timeClockSessionRepository.findOverlapping(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );

    if (overlappingSession) {
      throw new TimeClockSessionOverlapException(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );
    }

    const timeClockSession = await this.timeClockSessionRepository.create(
      timeClockSessionDomain,
    );
    return timeClockSession;
  }

  async getTimeClockSessionList(
    vehicleId: VehicleDomain['id'],
    filter: BaseFilterDto,
  ) {
    const vehicle = await this.vehicleRepository.findOne({ id: vehicleId });
    if (!vehicle) {
      throw new VehicleNotFoundException(vehicleId);
    }

    const timeClockSessionDomain = await this.timeClockSessionRepository.find(
      vehicleId,
      filter,
    );
    return timeClockSessionDomain;
  }

  async getTimeClockSessionDetails(
    id: TimeClockSessionDomain['id'],
  ): Promise<TimeClockSessionDomain> {
    const timeClockSessionDomain =
      await this.timeClockSessionRepository.findOne({ id });
    if (!timeClockSessionDomain) {
      throw new TimeClockSessionNotFoundException(id);
    }
    return timeClockSessionDomain;
  }

  async updateTimeClockSessionDetails(
    timeClockSessionDomain: TimeClockSessionDomain,
  ): Promise<void> {
    const timeClockSession = await this.timeClockSessionRepository.findOne({
      id: timeClockSessionDomain.id,
    });
    if (!timeClockSession) {
      throw new TimeClockSessionNotFoundException(timeClockSessionDomain.id);
    }

    const vehicle = await this.vehicleRepository.findOne({
      id: timeClockSessionDomain.vehicleId,
    });
    if (!vehicle) {
      throw new VehicleNotFoundException(timeClockSessionDomain.vehicleId);
    }

    if (timeClockSessionDomain.endTime) {
      const duration =
        (new Date(timeClockSessionDomain.endTime).getTime() -
          new Date(timeClockSessionDomain.startTime).getTime()) /
        (1000 * 60 * 60);

      if (duration > this.MAX_SESSION_DURATION_HOURS) {
        throw new TimeClockSessionDurationExceededException(
          timeClockSessionDomain.id,
          duration,
          this.MAX_SESSION_DURATION_HOURS,
        );
      }

      if (duration <= 0) {
        throw new TimeClockSessionOperationNotAllowedException(
          '',
          'update',
          'End time must be after start time',
        );
      }
    }

    const overlappingSession =
      await this.timeClockSessionRepository.findOverlapping(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
        timeClockSessionDomain.id,
      );

    if (overlappingSession) {
      throw new TimeClockSessionOverlapException(
        timeClockSessionDomain.vehicleId,
        timeClockSessionDomain.startTime,
        timeClockSessionDomain.endTime,
      );
    }

    await this.timeClockSessionRepository.update(timeClockSessionDomain);
    return;
  }

  async deleteTimeClockSession(
    id: TimeClockSessionDomain['id'],
  ): Promise<void> {
    const timeClockSession = await this.timeClockSessionRepository.findOne({
      id,
    });
    if (!timeClockSession) {
      throw new TimeClockSessionNotFoundException(id);
    }

    timeClockSession.isDeleted = true;
    timeClockSession.deletedAt = new Date();
    await this.timeClockSessionRepository.update(timeClockSession);
    return;
  }
}
