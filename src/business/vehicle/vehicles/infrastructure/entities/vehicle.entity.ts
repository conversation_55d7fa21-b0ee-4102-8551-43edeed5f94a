import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { VehicleTypeEntity } from '../../../vehicle-types/infrastructure/entities/vehicle-type.entity';

@Entity('vehicles')
export class VehicleEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @Column('uuid', { name: 'vehicleTypeId' })
  vehicleTypeId: string;

  @AutoMap()
  @ManyToOne(() => VehicleTypeEntity)
  @JoinColumn({ name: 'vehicleTypeId' })
  @Index()
  vehicleType: VehicleTypeEntity;

  @AutoMap()
  @Column()
  fleetId: string;

  @AutoMap()
  @Column({ length: 100 })
  make: string;

  @AutoMap()
  @Column({ length: 100 })
  model: string;

  @AutoMap()
  @Column()
  year: number;

  @AutoMap()
  @Column({ length: 50 })
  licensePlate: string;

  @AutoMap()
  @Column({ length: 50, nullable: true })
  vin: string;

  @AutoMap()
  @Column({ default: 'active' })
  status: string;

  @AutoMap()
  @Column({ nullable: true })
  ownedBy: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: [] })
  packageType: Array<string>;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxWeight: number;

  @AutoMap()
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxVolume: number;

  @AutoMap()
  @Column({ default: 0 })
  currentOdometer: number;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true })
  lastMaintenanceDate: Date;

  @AutoMap()
  @Column({ type: 'timestamptz', nullable: true })
  nextMaintenanceDue: Date;

  @AutoMap()
  @Column({ type: 'int', nullable: true })
  maintenanceIntervalKm: number;

  //current_driver_profile_id

  //default_driver_profile_id

  @AutoMap()
  @Column()
  branch: string;

  @AutoMap()
  @Column({ nullable: true })
  imageUrl: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  notes: string;

  @AutoMap()
  @Column({ type: 'jsonb', default: {} })
  metaData: Record<string, any>;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
