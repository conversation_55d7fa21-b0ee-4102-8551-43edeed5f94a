import { Injectable } from '@nestjs/common';
import { OrdersService } from '../orders.service';
import { PackageTemplatesService } from '../../package-templates/package-templates.service';
import { AddressService } from '@app/business/address/addresses/address.service';
import { CreateOrderDto } from '../dto/create-order.dto';
import { EnhancedOrderDto } from '../dto/enhanced-order.dto';
import { CreatePackageTemplateDto } from '../../package-templates/dto/create-package-template.dto';
import { OrderStatus } from '../domain/order.types';
import { faker } from '@faker-js/faker';
import { OrderResponseDto } from '../dto/order-response.dto';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import { PriceSetDomain } from '@app/business/pricing/price-sets/domain/price-set';
import { PriceSetPaymentOption } from '@app/business/pricing/price-sets/domain/price-set.types';
import { CreateOrderItemDto } from '../dto/create-order-item.dto';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import { UsersService } from '@app/business/user/users/users.service';

import { AddressDomain } from '@app/business/address/addresses/domain/address';

export interface SeedOrderOptions {
  count: number;
  tenantId: string;
  contactId: string;
  customerId?: string;
  useExistingAddresses?: boolean;
  useExistingPackageTemplates?: boolean;
  status?: OrderStatus;
  scheduledDays?: number; // Days from now for scheduled collection
}

@Injectable()
export class OrderSeederService {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly packageTemplatesService: PackageTemplatesService,
    private readonly addressService: AddressService,
    private readonly priceSetsService: PriceSetsService,
    private readonly contactsService: ContactsService,
    private readonly usersService: UsersService,
  ) { }

  /**
   * Seed orders for testing purposes
   */
  async seedOrders(options: SeedOrderOptions): Promise<OrderResponseDto[]> {
    const { count, tenantId, contactId } = options;

    // Get contact details to ensure it exists and get customer ID if not provided
    const contact = await this.contactsService.findById(contactId);
    if (!contact) {
      throw new Error(`Contact with ID ${contactId} not found`);
    }

    // Use provided customerId or get it from the contact
    const customerId = options.customerId || contact.userId;

    // Verify the customer exists
    const customer = await this.usersService.findById(customerId);
    if (!customer) {
      throw new Error(`Customer with ID ${customerId} not found`);
    }

    // Get the user ID associated with the contact for the createdBy field
    const userId = contact.userId;
    if (!userId) {
      throw new Error('Contact does not have an associated user ID');
    }

    // Get or create package templates
    const packageTemplates = options.useExistingPackageTemplates
      ? await this.getExistingPackageTemplates(tenantId)
      : await this.createPackageTemplates(tenantId, contactId, 3);

    if (packageTemplates.length === 0) {
      throw new Error('No package templates available');
    }

    // Get or create addresses
    const addresses = options.useExistingAddresses
      ? await this.getExistingAddresses(customerId)
      : await this.createAddresses(tenantId, customerId, contactId, 4);

    if (addresses.length < 2) {
      throw new Error('Not enough addresses available (need at least 2)');
    }

    // Get or create price sets
    let priceSets = await this.priceSetsService.getAllPriceSets(tenantId);
    if (priceSets.length === 0) {
      // Create price sets if none exist
      priceSets = await this.createPriceSets(tenantId, userId, 2);
      if (priceSets.length === 0) {
        throw new Error('Failed to create price sets');
      }
    }

    // Create orders
    const orders: OrderResponseDto[] = [];

    for (let i = 0; i < count; i++) {
      // Create order
      const order = await this.createOrder(
        tenantId,
        contactId,
        customerId,
        packageTemplates,
        addresses,
        priceSets,
        options,
        userId, // Pass the user ID to the createOrder method
      );

      orders.push(order);
    }

    return orders;
  }

  /**
   * Create a single order with all necessary data
   */
  private async createOrder(
    tenantId: string,
    contactId: string,
    customerId: string,
    packageTemplates: any[],
    addresses: any[],
    priceSets: any[],
    options: SeedOrderOptions,
    userId: string,
  ): Promise<OrderResponseDto> {
    // Randomly select addresses and price set
    // Package template is no longer used
    const pickupAddress = faker.helpers.arrayElement(addresses);

    // Ensure delivery address is different from pickup
    let deliveryAddress: any = null;
    do {
      deliveryAddress = faker.helpers.arrayElement(addresses);
    } while (deliveryAddress.id === pickupAddress.id);

    const priceSet = faker.helpers.arrayElement(priceSets);

    // Generate scheduled times if needed
    const now = new Date();
    const scheduledDays =
      options.scheduledDays || faker.number.int({ min: 1, max: 7 });

    const scheduledCollectionTime = new Date(now);
    scheduledCollectionTime.setDate(
      scheduledCollectionTime.getDate() + scheduledDays,
    );
    scheduledCollectionTime.setHours(
      faker.number.int({ min: 8, max: 17 }),
      0,
      0,
      0,
    );

    // Delivery time is now calculated by the system

    // Create order DTO
    const createOrderDto: CreateOrderDto = {
      referenceNumber: `REF-${faker.string.alphanumeric(8).toUpperCase()}`,
      // status is now set to 'Submitted' in the service

      collectionAddressId: pickupAddress.id,
      collectionContactName: faker.person.fullName(),
      collectionInstructions: faker.helpers.arrayElement([
        'Please call before arrival',
        'Use back entrance',
        'Ring doorbell twice',
        '',
      ]),
      collectionSignatureRequired: faker.datatype.boolean(),

      deliveryAddressId: deliveryAddress.id,
      deliveryContactName: faker.person.fullName(),
      deliveryInstructions: faker.helpers.arrayElement([
        'Leave with receptionist',
        'Call upon arrival',
        'Deliver to loading dock',
        '',
      ]),
      deliverySignatureRequired: faker.datatype.boolean(),

      scheduledCollectionTime,

      priceSetId: priceSet.id,
      // basePriceType and distanceUnit are now handled in the backend

      description: faker.helpers.arrayElement([
        'Urgent delivery',
        'Fragile items',
        'Standard delivery',
        'Heavy items',
        '',
      ]),

      items: this.generateOrderItems(3),
    };

    // Create enhanced order DTO with customer information
    const enhancedOrderDto: EnhancedOrderDto = {
      ...createOrderDto,
      customerId,
      requestedById: contactId,
      submittedById: contactId,
    };

    // Create the order using the user ID instead of the contact ID
    return this.ordersService.createOrder(tenantId, userId, enhancedOrderDto);
  }

  /**
   * Generate random order items
   */
  private generateOrderItems(count: number): CreateOrderItemDto[] {
    const items: CreateOrderItemDto[] = [];

    for (let i = 0; i < count; i++) {
      const itemType = faker.helpers.arrayElement([
        'Document Envelope',
        'Small Box',
        'Medium Box',
        'Large Box',
        'Pallet',
        'Fragile Package',
      ]);

      items.push({
        itemType,
        //@ts-expect-error packageTemplateId is null so added name statically
        packageTemplateName: faker.helpers.arrayElement([
          'Document Envelope',
          'Small Box',
          'Medium Box',
          'Large Box',
          'Pallet',
          'Fragile Package',
        ]),
        quantity: faker.number.int({ min: 1, max: 5 }),
        weight: faker.number.float({
          min: 0.1,
          max: 50,
          fractionDigits: 1,
        }),
        weightUnit: 'kg',
        length: faker.number.float({
          min: 10,
          max: 100,
          fractionDigits: 1,
        }),
        width: faker.number.float({
          min: 10,
          max: 100,
          fractionDigits: 1,
        }),
        height: faker.number.float({
          min: 10,
          max: 100,
          fractionDigits: 1,
        }),
        dimensionUnit: 'cm',
        description: faker.lorem.sentence(),
        declaredValue: faker.number.float({
          min: 10,
          max: 1000,
          fractionDigits: 2,
        }),
        notes: faker.helpers.maybe(() => faker.lorem.sentence()),
      });
    }

    return items;
  }

  /**
   * Get existing package templates or create new ones
   */
  private async getExistingPackageTemplates(tenantId: string): Promise<any[]> {
    const result = await this.packageTemplatesService.findAll(tenantId, {
      limit: 10,
    } as any);
    return result.data || [];
  }

  /**
   * Create new package templates
   */
  private async createPackageTemplates(
    tenantId: string,
    contactId: string,
    count: number,
  ): Promise<any[]> {
    const templates: any[] = [];

    // Get the contact to find the associated user ID
    const contact = await this.contactsService.findById(contactId);
    if (!contact) {
      throw new Error(`Contact with ID ${contactId} not found`);
    }

    // Use the user ID associated with the contact for the createdBy field
    const userId = contact.userId;
    if (!userId) {
      throw new Error('Contact does not have an associated user ID');
    }

    const packageTypes = [
      'Document Envelope',
      'Small Package',
      'Medium Package',
      'Large Package',
      'Extra Large Package',
      'Pallet',
    ];

    for (let i = 0; i < count; i++) {
      const packageType = packageTypes[i % packageTypes.length];

      const createDto: CreatePackageTemplateDto = {
        name: `${packageType} Template`,
        description: `Standard ${packageType.toLowerCase()} for shipping`,
        // Required field
        capabilities: [packageType],
        // Optional fields
        dimensionsRequired: true,
        weightRequired: true,
        maxWeight: faker.number.float({
          min: 30,
          max: 100,
          fractionDigits: 1,
        }),

        //length width and height are now required fields
        length: faker.number.float({
          min: 20,
          max: 100,
        }),
        height: faker.number.float({
          min: 10,
          max: 50,
        }),
        width: faker.number.float({
          min: 10,
          max: 50,
        }),
        // Add metadata for default values since they're not in the DTO
        metadata: {
          defaultWeight: faker.number.float({
            min: 0.1,
            max: 30,
            fractionDigits: 1,
          }),
          defaultLength: faker.number.float({
            min: 10,
            max: 100,
            fractionDigits: 1,
          }),
          defaultWidth: faker.number.float({
            min: 10,
            max: 100,
            fractionDigits: 1,
          }),
          defaultHeight: faker.number.float({
            min: 5,
            max: 50,
            fractionDigits: 1,
          }),
          defaultValue: faker.number.float({
            min: 10,
            max: 500,
            fractionDigits: 2,
          }),
        },
      };

      const template = await this.packageTemplatesService.create(
        tenantId,
        userId, // Use the user ID instead of the contact ID
        createDto,
      );
      templates.push(template);
    }

    return templates;
  }

  /**
   * Get existing addresses
   */
  private async getExistingAddresses(customerId: string): Promise<any[]> {
    const result = await this.addressService.getAddressList(
      { limit: 10 } as any,
      customerId,
    );
    return result.data || [];
  }

  /**
   * Create new addresses
   */
  private async createAddresses(
    tenantId: string,
    customerId: string,
    contactId: string,
    count: number,
  ): Promise<any[]> {
    const addresses: any[] = [];

    for (let i = 0; i < count; i++) {
      // Create an AddressDomain object
      const addressDomain = new AddressDomain();
      addressDomain.tenantId = tenantId;
      addressDomain.customerId = customerId;
      addressDomain.name = faker.person.fullName();
      addressDomain.companyName = faker.company.name();
      addressDomain.addressLine1 = faker.location.streetAddress();
      addressDomain.addressLine2 = faker.helpers.arrayElement([
        faker.location.secondaryAddress(),
        '',
      ]);
      addressDomain.city = faker.location.city();
      addressDomain.province = faker.location.state();
      addressDomain.postalCode = faker.location.zipCode();
      addressDomain.country = faker.location.country();
      addressDomain.countryCode = '+1';
      addressDomain.phoneNumber = faker.phone.number();
      addressDomain.email = faker.internet.email();
      addressDomain.notes = faker.helpers.arrayElement([
        faker.lorem.sentence(),
        '',
      ]);
      addressDomain.isDeleted = false;
      addressDomain.createdAt = new Date();
      addressDomain.updatedAt = new Date();
      addressDomain.createdBy = contactId;
      addressDomain.updatedBy = contactId;
      addressDomain.isDefaultForPickup = i === 0;
      addressDomain.isDefaultForDelivery = i === 1;

      const address = await this.addressService.create(addressDomain);
      addresses.push(address);
    }

    return addresses;
  }

  /**
   * Create new price sets
   */
  private async createPriceSets(
    tenantId: string,
    userId: string,
    count: number,
  ): Promise<any[]> {
    const priceSets: any[] = [];

    const priceSetTypes = [
      'Standard Delivery',
      'Express Delivery',
      'Same Day Delivery',
      'Economy Delivery',
    ];

    for (let i = 0; i < count; i++) {
      const priceSetType = priceSetTypes[i % priceSetTypes.length];

      // Create a price set domain object
      const priceSetDomain = new PriceSetDomain();
      priceSetDomain.tenantId = tenantId;
      priceSetDomain.name = priceSetType;
      priceSetDomain.internalName = `${priceSetType} (Internal)`;
      priceSetDomain.paymentOption = PriceSetPaymentOption.Full;
      priceSetDomain.description = `${priceSetType} pricing for all customers`;
      priceSetDomain.notes = faker.lorem.sentence();
      priceSetDomain.isActive = true;
      priceSetDomain.isDeleted = false;
      priceSetDomain.createdBy = userId;
      priceSetDomain.updatedBy = userId;

      try {
        const priceSet = await this.priceSetsService.create(priceSetDomain);
        priceSets.push(priceSet);
      } catch (error) {
        console.error(`Failed to create price set: ${error.message}`);
      }
    }

    return priceSets;
  }
}
