import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON><PERSON>ength,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateOrderItemDto } from './create-order-item.dto';

export class CreateOrderDto {
  @ApiPropertyOptional({
    example: 'REF-12345',
    description: 'Optional reference number (customer supplied)',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  referenceNumber?: string;

  // Collection Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection address ID - reference to an existing address',
  })
  @IsUUID('4')
  @IsNotEmpty()
  collectionAddressId: string;

  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Contact name for collection',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  collectionContactName?: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell and wait for security',
    description: 'Special instructions for collection',
  })
  @IsString()
  @IsOptional()
  collectionInstructions?: string;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether signature is required for collection',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  collectionSignatureRequired?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T09:00:00Z',
    description: 'Scheduled collection time',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  scheduledCollectionTime?: Date;

  // Delivery Information
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery address ID - reference to an existing address',
  })
  @IsUUID('4')
  @IsNotEmpty()
  deliveryAddressId: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Contact name for delivery',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  deliveryContactName?: string;

  @ApiPropertyOptional({
    example: 'Leave at front desk if no answer',
    description: 'Special instructions for delivery',
  })
  @IsString()
  @IsOptional()
  deliveryInstructions?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether signature is required for delivery',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  deliverySignatureRequired?: boolean;

  // scheduledDeliveryTime is now calculated by the system based on scheduledCollectionTime and price set

  // Package Information is now calculated from items
  // No need to provide totalItems, totalWeight, totalVolume

  @ApiPropertyOptional({
    example: 500,
    description: 'Declared value of the shipment',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  declaredValue?: number;

  // Vehicle and Assignment Information
  // Vehicle type, driver, and vehicle are now assigned in the backend

  @ApiPropertyOptional({
    example: 100.0,
    description: 'Cash on delivery amount',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  codAmount?: number;

  // Pricing Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Price set ID',
  })
  @IsUUID('4')
  @IsOptional()
  priceSetId?: string;

  // Base price type is now determined in the backend

  // Stats/Metrics
  // Distance is now calculated in the backend

  // Additional Fields
  @ApiPropertyOptional({
    example: 'Urgent delivery of medical supplies',
    description: 'Order description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    example: 'Customer requested expedited shipping',
    description: 'Comments about the order',
  })
  @IsString()
  @IsOptional()
  comments?: string;

  @ApiPropertyOptional({
    example: 'VIP customer - handle with care',
    description: 'Internal notes (not visible to customer)',
  })
  @IsString()
  @IsOptional()
  internalNotes?: string;

  @ApiProperty({
    type: [CreateOrderItemDto],
    description: 'Items to include in this order',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  @IsNotEmpty()
  items: CreateOrderItemDto[];

  @ApiPropertyOptional({
    example: {
      department: 'Electronics',
      priority: 'High',
    },
    description: 'Custom fields',
  })
  @IsObject()
  @IsOptional()
  customFields?: Record<string, any>;

  @ApiPropertyOptional({
    example: {
      originalRequestId: '12345',
      customerSystem: 'ERP-XYZ',
    },
    description: 'Additional metadata',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
