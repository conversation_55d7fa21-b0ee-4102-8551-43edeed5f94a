import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, In, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { AddressEntity } from '../../../../address/addresses/infrastructure/entities/address.entity';
import { AddressDomain } from '../../domain/address';
import { UserEntity } from '../../../../user/users/infrastructure/entities/user.entity';
import { UserNotFoundException } from '@utils/errors/exceptions/user.exceptions';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../../address-filter.config';
import { ContactAddressPreferenceEntity } from '../../../../address/addresses/infrastructure/entities/contact-address-preferences.entity';
import { AddressPreferenceDomain } from '../../domain/address-preference';
import { ContactNotFoundException } from '../../../../../utils/errors/exceptions/contact.exceptions';
import { AddressNotFoundException } from '../../../../../utils/errors/exceptions/address-exceptions';
import { ContactEntity } from '../../../../user/contacts/infrastructure/persistence/relational/entities/contact.entity';

@Injectable()
export class AddressRepository {
  constructor(
    @InjectRepository(AddressEntity)
    private readonly addressRepository: Repository<AddressEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(ContactEntity)
    private readonly contactRepository: Repository<ContactEntity>,
    @InjectRepository(ContactAddressPreferenceEntity)
    private readonly contactAddressPreferenceRepository: Repository<ContactAddressPreferenceEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(AddressFilterConfig());
  }

  async create(data: AddressDomain): Promise<AddressDomain> {
    const requestEntity = this.mapper.map(data, AddressDomain, AddressEntity);
    const user = await this.userRepository.findOne({
      where: { id: requestEntity.customerId },
    });
    if (!user) {
      throw new UserNotFoundException(requestEntity.customerId);
    }

    const addressEntity = await this.addressRepository.save(
      this.addressRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      addressEntity,
      AddressEntity,
      AddressDomain,
    );
    return responseDomain;
  }

  async createAddressPref(data: AddressPreferenceDomain): Promise<AddressPreferenceDomain> {
    const requestEntity = this.mapper.map(data, AddressPreferenceDomain, ContactAddressPreferenceEntity);

    const contact = await this.contactRepository.findOne({
      where: { id: requestEntity.contactId },
    });
    if (!contact) {
      throw new ContactNotFoundException(requestEntity.contactId);
    }

    const address = await this.addressRepository.findOne({
      where: { id: requestEntity.addressId },
    });
    if (!address) {
      throw new AddressNotFoundException(requestEntity.addressId);
    }


    const addressEntity = await this.contactAddressPreferenceRepository.save(
      this.contactAddressPreferenceRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      addressEntity,
      ContactAddressPreferenceEntity,
      AddressPreferenceDomain,
    );
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    customerId: string,
  ): Promise<PaginatedResult<AddressDomain>> {
    const queryBuilder = this.addressRepository
      .createQueryBuilder('address')
      .where('address.isDeleted = false')
      .andWhere('address.customerId = :customerId', { customerId })
      .leftJoinAndSelect('address.customer', 'customer');

    const user = await this.userRepository.findOne({
      where: { id: customerId },
    });
    if (!user) {
      throw new UserNotFoundException(customerId);
    }

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      AddressEntity,
      AddressDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<AddressDomain>,
  ): Promise<NullableType<AddressDomain>> {
    const requestEntity: Partial<AddressEntity> = this.mapper.map(
      fields,
      AddressDomain,
      AddressEntity,
    );
    const addressEntity = await this.addressRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<AddressEntity>),
        isDeleted: false,
      },
      relations: ['customer'],
    });
    if (addressEntity) {
      const responseDomain = this.mapper.map(
        addressEntity,
        AddressEntity,
        AddressDomain,
      );
      return responseDomain;
    }
    return null;
  }


  async findOneAddressPref(
    fields: EntityCondition<AddressPreferenceDomain>,
  ): Promise<NullableType<AddressPreferenceDomain>> {
    const requestEntity: Partial<ContactAddressPreferenceEntity> = this.mapper.map(
      fields,
      AddressPreferenceDomain,
      ContactAddressPreferenceEntity,
    );
    const addressPrefEntity = await this.contactAddressPreferenceRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<ContactAddressPreferenceEntity>),
        //  isDeleted: false,
      },
      relations: ['contact', 'address'],
    });
    if (addressPrefEntity) {
      const responseDomain = this.mapper.map(
        addressPrefEntity,
        ContactAddressPreferenceEntity,
        AddressPreferenceDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: AddressDomain): Promise<AddressDomain> {
    const requestEntity = this.mapper.map(
      payload,
      AddressDomain,
      AddressEntity,
    );
    const addressEntity = await this.addressRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      addressEntity,
      AddressEntity,
      AddressDomain,
    );
    return responseDomain;
  }

  async updateAddressPreference(payload: AddressPreferenceDomain): Promise<AddressPreferenceDomain> {
    const requestEntity = this.mapper.map(
      payload,
      AddressPreferenceDomain,
      ContactAddressPreferenceEntity,
    );
    const addressPrefEntity = await this.contactAddressPreferenceRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      addressPrefEntity,
      ContactAddressPreferenceEntity,
      AddressPreferenceDomain,
    );
    return responseDomain;
  }

  async findDefaultForPickup(
    contactId: string,
  ): Promise<AddressPreferenceDomain | null> {
    const address = await this.contactAddressPreferenceRepository.findOne({
      where: {
        isDefaultForPickup: true,
        contactId,
        // isDeleted: false,
      },
    });
    return address
      ? this.mapper.map(address, ContactAddressPreferenceEntity, AddressPreferenceDomain)
      : null;
  }

  async findDefaultForDelivery(
    contactId: string,
  ): Promise<AddressPreferenceDomain | null> {
    const address = await this.contactAddressPreferenceRepository.findOne({
      where: {
        isDefaultForDelivery: true,
        contactId,
        //isDeleted: false,
      },
    });
    return address
      ? this.mapper.map(address, ContactAddressPreferenceEntity, AddressPreferenceDomain)
      : null;
  }

  async findPreferencesForAddresses(
    addressIds: string[],
    contactId: string,
  ): Promise<ContactAddressPreferenceEntity[]> {
    return this.contactAddressPreferenceRepository.find({
      where: {
        address: { id: In(addressIds) },
        contact: { id: contactId },
      },
      relations: ['address'],
    });
  }


  /**
   * Create a query builder for address entities
   * @returns TypeORM query builder for address entities
   */
  createQueryBuilder(alias: string) {
    return this.addressRepository.createQueryBuilder(alias);
  }
}
