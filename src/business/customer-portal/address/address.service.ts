import { Injectable } from '@nestjs/common';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { AddressRepository } from './infrastructure/repositories/address.repository';
import { AddressDomain } from './domain/address';
import { AddressEntity } from '../../address/addresses/infrastructure/entities/address.entity';
import {
  AddressNotFoundException,
} from '@utils/errors/exceptions/address-exceptions';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { AddressPreferenceDomain } from './domain/address-preference';
import { ContactAddressPreferenceEntity } from '../../address/addresses/infrastructure/entities/contact-address-preferences.entity';
import { GetAddressDto } from './dto/get-address.dto';

@Injectable()
export class AddressService {
  constructor(
    private readonly addressRepository: AddressRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) { }

  async create(addressDomain: AddressDomain): Promise<AddressDomain> {
    // Check if this is set as default and update other addresses accordingly
    if (addressDomain.isDefaultForPickup) {
      await this.clearDefaultForPickup(addressDomain.customerId);
    }

    if (addressDomain.isDefaultForDelivery) {
      await this.clearDefaultForDelivery(addressDomain.customerId);
    }
    return await this.addressRepository.create(addressDomain);
  }

  async createAddressPref(data: AddressPreferenceDomain): Promise<AddressPreferenceDomain> {
    if (data.isDefaultForPickup) {
      await this.clearDefaultForPickup(data.contactId);
    }

    if (data.isDefaultForDelivery) {
      await this.clearDefaultForDelivery(data.contactId);
    }

    const addressPreference = this.mapper.map(
      data,
      AddressPreferenceDomain,
      ContactAddressPreferenceEntity,
    );

    return await this.addressRepository.createAddressPref(addressPreference);
  }

  async duplicateAddress(
    addressId: AddressDomain['id'],
  ): Promise<AddressDomain> {
    const addressDomain = await this.addressRepository.findOne({
      id: addressId,
    });
    if (!addressDomain) {
      throw new AddressNotFoundException(addressId);
    }

    const duplicateAddress = this.mapper.map(
      addressDomain,
      AddressDomain,
      AddressDomain,
    );

    // Create a new address without the ID
    const newAddress = { ...duplicateAddress };
    // @ts-expect-error - We need to remove the ID to create a new record
    newAddress.id = undefined;
    newAddress.isDefaultForPickup = false;
    newAddress.isDefaultForDelivery = false;

    const savedAddress = await this.create(newAddress); // uses addressService.create()

    // 🔁 Fetch original preference
    const oldPref = await this.addressRepository.findOneAddressPref({ addressId });

    if (oldPref) {
      const newPref = new AddressPreferenceDomain();
      newPref.contactId = oldPref.contactId;
      newPref.addressId = savedAddress.id;
      newPref.tenantId = oldPref.tenantId;
      newPref.isDefaultForPickup = false;
      newPref.isDefaultForDelivery = false;
      newPref.isFavoriteForPickup = false;
      newPref.isFavoriteForDelivery = false;

      await this.createAddressPref(newPref);
    }

    return savedAddress;
  }

  async getAddressList(
    filter: BaseFilterDto,
    customerId: string,
    contactId: string,
  ): Promise<PaginatedResult<AddressDomain>> {
    const result: any = await this.addressRepository.find(filter, customerId);

    const mappedData = this.mapper.mapArray(
      result?.data ?? [],
      AddressDomain,
      GetAddressDto,
    );

    // Fetch preferences for each address for this contact
    const addressIds = mappedData.map((a) => a.id);

    const preferences = await this.addressRepository.findPreferencesForAddresses(
      addressIds,
      contactId,
    );

    const prefMap = new Map(
      preferences.map((pref) => [pref.address.id, pref]),
    );

    for (const dto of mappedData) {
      const pref = prefMap.get(dto.id);
      dto.isDefaultForPickup = pref?.isDefaultForPickup || false;
      dto.isDefaultForDelivery = pref?.isDefaultForDelivery || false;
      dto.isFavoriteForPickup = pref?.isFavoriteForPickup || false;
      dto.isFavoriteForDelivery = pref?.isFavoriteForDelivery || false;
    }
    return {
      ...result,
      data: mappedData,
    };
  }

  async getAddressDetails(addressId: string): Promise<AddressDomain> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }
    return address;
  }

  async updateAddressDetails(address: AddressDomain): Promise<void> {
    const existingAddress = await this.addressRepository.findOne({
      id: address.id,
    });
    if (!existingAddress) {
      throw new AddressNotFoundException(address.id);
    }

    await this.addressRepository.update(address);
  }

  async softDeleteAddress(addressId: string): Promise<void> {
    const address = await this.addressRepository.findOne({ id: addressId });
    if (!address) {
      throw new AddressNotFoundException(addressId);
    }

    address.isDeleted = true;
    address.deletedAt = new Date();
    await this.addressRepository.update(address);
  }

  private async clearDefaultForPickup(contactId: string): Promise<void> {
    const defaultAddress =
      await this.addressRepository.findDefaultForPickup(contactId);
    if (defaultAddress) {
      defaultAddress.isDefaultForPickup = false;
      await this.addressRepository.updateAddressPreference(defaultAddress);
    }
  }

  private async clearDefaultForDelivery(contactId: string): Promise<void> {
    const defaultAddress =
      await this.addressRepository.findDefaultForDelivery(contactId);
    if (defaultAddress) {
      defaultAddress.isDefaultForDelivery = false;
      await this.addressRepository.updateAddressPreference(defaultAddress);
    }
  }

  /**
   * Get all addresses for a customer without pagination
   * @param customerId The customer ID
   * @returns Array of address domains
   */
  async getAllAddresses(
    customerId: string,
    contactId: string,
  ): Promise<AddressDomain[]> {
    const queryBuilder = this.addressRepository
      .createQueryBuilder('address')
      .where('address.isDeleted = false')
      .andWhere('address.customerId = :customerId', { customerId })
      .leftJoinAndSelect('address.customer', 'customer')
      .orderBy('address.createdAt', 'DESC');

    const addressEntities = await queryBuilder.getMany();

    const addressDomains = this.mapper.mapArray(
      addressEntities,
      AddressEntity,
      AddressDomain,
    );

    const addressIds = addressDomains.map((a) => a.id);

    const preferences = await this.addressRepository.findPreferencesForAddresses(
      addressIds,
      contactId,
    );

    const prefMap = new Map(
      preferences.map((p) => [p.addressId, p]),
    );

    for (const domain of addressDomains) {
      const pref = prefMap.get(domain.id);
      domain.isDefaultForPickup = pref?.isDefaultForPickup ?? false;
      domain.isDefaultForDelivery = pref?.isDefaultForDelivery ?? false;
      domain.isFavoriteForPickup = pref?.isFavoriteForPickup ?? false;
      domain.isFavoriteForDelivery = pref?.isFavoriteForDelivery ?? false;
    }

    return addressDomains;
  }


  /**
   * Update address preferences (favorite/default status)
   * @param addressId The address ID
   * @param contactId The customer ID
   * @param preferences The preferences to update
   * @returns The updated address
   */
  async updateAddressPreferences(
    addressId: string,
    contactId: string,
    preferences: {
      isFavoriteForPickup?: boolean;
      isFavoriteForDelivery?: boolean;
      isDefaultForPickup?: boolean;
      isDefaultForDelivery?: boolean;
    },
    tenantId: string,
  ): Promise<AddressPreferenceDomain> {
    let pref: any = await this.addressRepository.findOneAddressPref({ addressId, contactId });
    if (!pref) {
      const newPref: Partial<AddressPreferenceDomain> = {
        contactId,
        addressId,
        isFavoriteForPickup: false,
        isFavoriteForDelivery: false,
        isDefaultForPickup: false,
        isDefaultForDelivery: false,
        tenantId
      };
      pref = await this.addressRepository.createAddressPref(newPref as AddressPreferenceDomain);
    }

    // Update favorite status
    if (preferences.isFavoriteForPickup !== undefined) {
      pref.isFavoriteForPickup = preferences.isFavoriteForPickup;
    }

    if (preferences.isFavoriteForDelivery !== undefined) {
      pref.isFavoriteForDelivery = preferences.isFavoriteForDelivery;
    }

    // Update default status - only for pickup as per requirements
    if (preferences.isDefaultForPickup !== undefined) {
      if (preferences.isDefaultForPickup) {
        await this.clearDefaultForPickup(contactId);
      }
      pref.isDefaultForPickup = preferences.isDefaultForPickup;
    }
    return await this.addressRepository.updateAddressPreference(pref);
  }

  async updateAddressPreference(payload: AddressPreferenceDomain): Promise<AddressPreferenceDomain> {
    if (payload.isDefaultForPickup) {
      await this.clearDefaultForPickup(payload.contactId);
    }

    if (payload.isDefaultForDelivery) {
      await this.clearDefaultForDelivery(payload.contactId);
    }

    const requestEntity = this.mapper.map(
      payload,
      AddressPreferenceDomain,
      ContactAddressPreferenceEntity,
    );

    return await this.addressRepository.createAddressPref(requestEntity);
  }


  async findOneAddressPrefService(
    addressId: string,
    contactId: string,
  ): Promise<AddressPreferenceDomain | null> {
    const entity = await this.addressRepository.findOneAddressPref({
      addressId,
      contactId,
    });

    if (!entity) return null;

    return this.mapper.map(
      entity,
      ContactAddressPreferenceEntity,
      AddressPreferenceDomain,
    );
  }

}


