{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "scripts": {"typeorm": "env-cmd ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migration:generate": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:generate", "postmigration:generate": "npm run lint -- --fix", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:run", "migration:revert": "npm run typeorm -- --dataSource=src/database/data-source.ts migration:revert", "schema:drop": "npm run typeorm -- --dataSource=src/database/data-source.ts schema:drop", "seed:create:relational": "hygen seeds create-relational", "generate:resource:relational": "hygen generate relational-resource", "postgenerate:resource:relational": "npm run lint -- --fix", "add:property:to-relational": "hygen property add-to-relational", "postadd:property:to-relational": "npm run lint -- --fix", "seed:run:relational": "ts-node -r tsconfig-paths/register ./src/database/seeds/relational/run-seed.ts", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:swc": "nest start -b swc -w", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "env-cmd jest --config ./test/jest-e2e.json", "test:e2e:relational:docker": "docker compose -f docker-compose.relational.test.yaml --env-file env-example-relational -p tests up -d --build && docker compose -f docker-compose.relational.test.yaml -p tests exec api /opt/wait-for-it.sh -t 0 localhost:3000 -- npm run test:e2e -- --watchAll --runInBand && docker compose -f docker-compose.relational.test.yaml -p tests down && docker compose -p tests rm -svf", "release": "release-it"}, "dependencies": {"@automapper/classes": "^8.8.1", "@automapper/core": "^8.8.1", "@automapper/nestjs": "^8.8.1", "@aws-sdk/client-s3": "3.670.0", "@aws-sdk/s3-request-presigner": "3.670.0", "@nestjs/common": "10.4.15", "@nestjs/config": "3.3.0", "@nestjs/core": "10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "10.0.3", "@nestjs/platform-express": "10.4.15", "@nestjs/swagger": "8.0.7", "@nestjs/typeorm": "10.0.2", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.55.0", "@opentelemetry/core": "^1.30.0", "@opentelemetry/exporter-trace-otlp-http": "^0.57.0", "@opentelemetry/sdk-node": "^0.57.0", "@opentelemetry/sdk-trace-base": "^1.30.0", "@opentelemetry/sdk-trace-node": "^1.30.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@types/multer-s3": "3.0.3", "bcrypt": "^5.1.1", "bcryptjs": "2.4.3", "class-transformer": "0.5.1", "class-validator": "0.14.1", "cookie-parser": "^1.4.7", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "handlebars": "4.7.8", "moment-timezone": "^0.6.0", "ms": "2.1.3", "multer": "1.4.5-lts.1", "multer-s3": "3.0.1", "nestjs-i18n": "10.5.0", "nodemailer": "6.9.16", "passport": "0.7.0", "passport-anonymous": "1.0.1", "passport-jwt": "4.0.1", "pg": "8.13.1", "posthog-node": "^4.17.2", "reflect-metadata": "^0.1.13", "rimraf": "6.0.1", "rxjs": "7.8.1", "source-map-support": "0.5.21", "sqlite3": "^5.1.7", "stripe": "^18.0.0", "swagger-ui-express": "5.0.1", "twilio": "^5.6.1", "typeorm": "0.3.20"}, "devDependencies": {"@commitlint/cli": "19.6.0", "@commitlint/config-conventional": "19.6.0", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.9.0", "@faker-js/faker": "^9.7.0", "@nestjs/cli": "10.4.9", "@nestjs/schematics": "10.2.3", "@nestjs/testing": "10.4.15", "@release-it/conventional-changelog": "9.0.2", "@swc/cli": "0.5.0", "@swc/core": "1.9.2", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "2.4.6", "@types/cookie-parser": "^1.4.8", "@types/express": "5.0.0", "@types/jest": "29.5.14", "@types/ms": "0.7.34", "@types/multer": "1.4.12", "@types/node": "22.10.2", "@types/passport-anonymous": "1.0.5", "@types/passport-jwt": "4.0.1", "@types/supertest": "6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "8.16.0", "@typescript-eslint/parser": "8.16.0", "env-cmd": "10.1.0", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "globals": "^15.9.0", "hygen": "6.2.11", "is-ci": "3.0.1", "jest": "29.7.0", "prettier": "3.3.3", "prompts": "2.4.2", "release-it": "17.10.0", "supertest": "7.0.0", "ts-jest": "29.2.5", "ts-loader": "9.5.1", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "tslib": "2.8.1", "typescript": "5.6.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@app/(.*)$": "<rootDir>/src/$1", "^@core/(.*)$": "<rootDir>/src/core/$1", "^@infrastructure/(.*)$": "<rootDir>/src/infrastructure/$1", "^@modules/(.*)$": "<rootDir>/src/modules/$1", "^@auth/(.*)$": "<rootDir>/src/modules/auth/$1", "^@users/(.*)$": "<rootDir>/src/modules/users/$1", "^@files/(.*)$": "<rootDir>/src/modules/files/$1", "^@tenants/(.*)$": "<rootDir>/src/modules/tenants/$1", "^@mail/(.*)$": "<rootDir>/src/modules/mail/$1", "^@config/(.*)$": "<rootDir>/src/config/$1", "^@database/(.*)$": "<rootDir>/src/database/$1", "^@utils/(.*)$": "<rootDir>/src/utils/$1", "^@common/(.*)$": "<rootDir>/src/common/$1"}}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "release-it": {"git": {"commitMessage": "chore: release v${version}"}, "github": {"release": true}, "npm": {"publish": false}, "plugins": {"@release-it/conventional-changelog": {"infile": "CHANGELOG.md", "preset": {"name": "conventionalcommits", "types": [{"type": "chore(deps)", "section": "Dependency Upgrades"}, {"type": "fix(deps)", "section": "Dependency Upgrades"}, {"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "perf", "section": "Performance Improvements"}, {"type": "revert", "section": "Reverts"}, {"type": "docs", "section": "Documentation"}, {"type": "refactor", "section": "Code Refactoring"}, {"type": "test", "section": "Tests"}, {"type": "ci", "section": "Continuous Integration"}]}}}}}